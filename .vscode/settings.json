{"cmake.configureSettings": {"CMAKE_TOOLCHAIN_FILE": "${env:HOME}/vcpkg/scripts/buildsystems/vcpkg.cmake"}, "cmake.buildDirectory": "${workspaceFolder}/build", "cmake.generator": "Unix Makefiles", "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "C_Cpp.default.compilerPath": "/opt/homebrew/bin/gcc-15", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.intelliSenseMode": "gcc-arm64", "files.associations": {"*.h": "c", "*.hpp": "cpp", "*.cpp": "cpp", "*.cc": "cpp", "*.cxx": "cpp"}}