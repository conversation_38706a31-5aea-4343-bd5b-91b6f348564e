{"version": "2.0.0", "tasks": [{"label": "Build Redis Server", "type": "shell", "command": "./your_program.sh", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$gcc"], "options": {"env": {"VCPKG_ROOT": "${env:HOME}/vcpkg"}}}, {"label": "Clean Build", "type": "shell", "command": "rm", "args": ["-rf", "build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Run Redis Server", "type": "shell", "command": "./build/redis", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "dependsOn": "Build Redis Server"}]}